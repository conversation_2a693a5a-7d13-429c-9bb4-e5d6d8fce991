const express = require('express');
const { body, validationResult } = require('express-validator');
const { executeQuery, executeQuerySingle } = require('../config/database');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// 获取用户的所有项目
router.get('/', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const projects = await executeQuery(
      `SELECT id, user_id, project_name, description, natural_language_input, 
              structured_parameters, status, created_at, updated_at 
       FROM projects 
       WHERE user_id = ? 
       ORDER BY updated_at DESC`,
      [userId]
    );

    res.json({
      success: true,
      data: {
        projects
      }
    });

  } catch (error) {
    console.error('获取项目列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 获取单个项目详情
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const projectId = req.params.id;
    const userId = req.user.id;

    const project = await executeQuerySingle(
      `SELECT id, user_id, project_name, description, natural_language_input, 
              structured_parameters, status, created_at, updated_at 
       FROM projects 
       WHERE id = ? AND user_id = ?`,
      [projectId, userId]
    );

    if (!project) {
      return res.status(404).json({
        success: false,
        message: '项目不存在或无权访问'
      });
    }

    res.json({
      success: true,
      data: {
        project
      }
    });

  } catch (error) {
    console.error('获取项目详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 创建新项目
router.post('/', authenticateToken, [
  body('project_name').isLength({ min: 1, max: 255 }).withMessage('项目名称长度应在1-255个字符之间'),
  body('description').optional().isLength({ max: 1000 }).withMessage('项目描述不能超过1000个字符'),
  body('natural_language_input').optional().isLength({ max: 5000 }).withMessage('自然语言输入不能超过5000个字符'),
  body('structured_parameters').optional().isJSON().withMessage('结构化参数必须是有效的JSON格式')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const { 
      project_name, 
      description = '', 
      natural_language_input = '', 
      structured_parameters = '{}' 
    } = req.body;
    const userId = req.user.id;

    // 插入新项目
    const result = await executeQuery(
      `INSERT INTO projects (user_id, project_name, description, natural_language_input, 
                           structured_parameters, status, created_at, updated_at) 
       VALUES (?, ?, ?, ?, ?, 'active', NOW(), NOW())`,
      [userId, project_name, description, natural_language_input, structured_parameters]
    );

    const projectId = result.insertId;

    // 获取新创建的项目信息
    const newProject = await executeQuerySingle(
      `SELECT id, user_id, project_name, description, natural_language_input, 
              structured_parameters, status, created_at, updated_at 
       FROM projects 
       WHERE id = ?`,
      [projectId]
    );

    res.status(201).json({
      success: true,
      message: '项目创建成功',
      data: {
        project: newProject
      }
    });

  } catch (error) {
    console.error('创建项目错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 更新项目
router.put('/:id', authenticateToken, [
  body('project_name').optional().isLength({ min: 1, max: 255 }).withMessage('项目名称长度应在1-255个字符之间'),
  body('description').optional().isLength({ max: 1000 }).withMessage('项目描述不能超过1000个字符'),
  body('natural_language_input').optional().isLength({ max: 5000 }).withMessage('自然语言输入不能超过5000个字符'),
  body('structured_parameters').optional().isJSON().withMessage('结构化参数必须是有效的JSON格式'),
  body('status').optional().isIn(['active', 'archived', 'completed']).withMessage('项目状态无效')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: '输入验证失败',
        errors: errors.array()
      });
    }

    const projectId = req.params.id;
    const userId = req.user.id;

    // 检查项目是否存在且属于当前用户
    const existingProject = await executeQuerySingle(
      'SELECT id FROM projects WHERE id = ? AND user_id = ?',
      [projectId, userId]
    );

    if (!existingProject) {
      return res.status(404).json({
        success: false,
        message: '项目不存在或无权访问'
      });
    }

    const { 
      project_name, 
      description, 
      natural_language_input, 
      structured_parameters, 
      status 
    } = req.body;

    // 构建更新查询
    const updates = [];
    const values = [];

    if (project_name !== undefined) {
      updates.push('project_name = ?');
      values.push(project_name);
    }
    if (description !== undefined) {
      updates.push('description = ?');
      values.push(description);
    }
    if (natural_language_input !== undefined) {
      updates.push('natural_language_input = ?');
      values.push(natural_language_input);
    }
    if (structured_parameters !== undefined) {
      updates.push('structured_parameters = ?');
      values.push(structured_parameters);
    }
    if (status !== undefined) {
      updates.push('status = ?');
      values.push(status);
    }

    if (updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: '没有提供要更新的字段'
      });
    }

    updates.push('updated_at = NOW()');
    values.push(projectId, userId);

    await executeQuery(
      `UPDATE projects SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
      values
    );

    // 获取更新后的项目信息
    const updatedProject = await executeQuerySingle(
      `SELECT id, user_id, project_name, description, natural_language_input, 
              structured_parameters, status, created_at, updated_at 
       FROM projects 
       WHERE id = ?`,
      [projectId]
    );

    res.json({
      success: true,
      message: '项目更新成功',
      data: {
        project: updatedProject
      }
    });

  } catch (error) {
    console.error('更新项目错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 删除项目
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const projectId = req.params.id;
    const userId = req.user.id;

    // 检查项目是否存在且属于当前用户
    const existingProject = await executeQuerySingle(
      'SELECT id FROM projects WHERE id = ? AND user_id = ?',
      [projectId, userId]
    );

    if (!existingProject) {
      return res.status(404).json({
        success: false,
        message: '项目不存在或无权访问'
      });
    }

    // 删除项目
    await executeQuery(
      'DELETE FROM projects WHERE id = ? AND user_id = ?',
      [projectId, userId]
    );

    res.json({
      success: true,
      message: '项目删除成功'
    });

  } catch (error) {
    console.error('删除项目错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
