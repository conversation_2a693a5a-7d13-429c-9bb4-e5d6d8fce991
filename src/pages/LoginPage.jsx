import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Canvas, useFrame } from '@react-three/fiber';
import { Stars, OrbitControls } from '@react-three/drei';
import * as THREE from 'three';
import '../styles/LoginPage.css';

// 粒子背景组件
function ParticleBackground() {
  const pointsRef = useRef();
  const particleCount = 1000;

  const positions = React.useMemo(() => {
    const pos = new Float32Array(particleCount * 3);
    for (let i = 0; i < particleCount; i++) {
      pos[i * 3] = (Math.random() - 0.5) * 20;
      pos[i * 3 + 1] = (Math.random() - 0.5) * 20;
      pos[i * 3 + 2] = (Math.random() - 0.5) * 20;
    }
    return pos;
  }, []);

  useFrame(({ clock }) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y = clock.getElapsedTime() * 0.05;
      pointsRef.current.rotation.x = clock.getElapsedTime() * 0.02;
    }
  });

  return (
    <points ref={pointsRef}>
      <bufferGeometry>
        <bufferAttribute
          attach="attributes-position"
          count={particleCount}
          array={positions}
          itemSize={3}
        />
      </bufferGeometry>
      <pointsMaterial
        size={0.02}
        color="#4dc8ff"
        transparent
        opacity={0.6}
        sizeAttenuation={false}
      />
    </points>
  );
}

// 3D背景场景
function Background3D() {
  return (
    <Canvas
      camera={{ position: [0, 0, 5], fov: 75 }}
      style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
    >
      <color attach="background" args={["#0a0f1c"]} />
      <ParticleBackground />
      <Stars
        radius={100}
        depth={50}
        count={2000}
        factor={4}
        saturation={0}
        fade
        speed={0.5}
      />
      <OrbitControls
        enableZoom={false}
        enablePan={false}
        enableRotate={false}
        autoRotate={true}
        autoRotateSpeed={0.5}
      />
    </Canvas>
  );
}

// 主登录页面组件
function LoginPage() {
  const navigate = useNavigate();
  const [isLogin, setIsLogin] = useState(true);
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    username: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState('');
  const [formProgress, setFormProgress] = useState(0);

  const isEnglish = language === 'en';

  // 表单文本内容
  const texts = {
    en: {
      welcome: 'Welcome to',
      appName: 'Industrial Geo Explorer',
      subtitle: 'Advanced Geographic Intelligence Platform',
      loginTitle: 'Sign In',
      registerTitle: 'Create Account',
      email: 'Email Address',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      username: 'Username',
      loginButton: 'Sign In',
      registerButton: 'Create Account',
      switchToRegister: "Don't have an account? Sign up",
      switchToLogin: 'Already have an account? Sign in',
      backToHome: 'Back to Home',
      forgotPassword: 'Forgot Password?',
      rememberMe: 'Remember me',
      orDivider: 'OR',
      googleLogin: 'Continue with Google',
      githubLogin: 'Continue with GitHub'
    },
    zh: {
      welcome: '欢迎使用',
      appName: '工域探索',
      subtitle: '先进地理智能平台',
      loginTitle: '登录',
      registerTitle: '注册账户',
      email: '邮箱地址',
      password: '密码',
      confirmPassword: '确认密码',
      username: '用户名',
      loginButton: '登录',
      registerButton: '注册',
      switchToRegister: '没有账户？立即注册',
      switchToLogin: '已有账户？立即登录',
      backToHome: '返回首页',
      forgotPassword: '忘记密码？',
      rememberMe: '记住我',
      orDivider: '或',
      googleLogin: '使用 Google 登录',
      githubLogin: '使用 GitHub 登录'
    }
  };

  const t = texts[language];

  // 密码强度检测函数
  const checkPasswordStrength = (password) => {
    if (!password) return '';

    let score = 0;
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;

    if (score < 3) return 'weak';
    if (score < 5) return 'medium';
    return 'strong';
  };

  // 计算表单完成进度
  const calculateFormProgress = () => {
    let progress = 0;
    const totalFields = isLogin ? 2 : 4;

    if (formData.email) progress++;
    if (formData.password) progress++;
    if (!isLogin) {
      if (formData.username) progress++;
      if (formData.confirmPassword) progress++;
    }

    return (progress / totalFields) * 100;
  };

  // 表单验证函数
  const validateForm = () => {
    const newErrors = {};

    // 邮箱验证
    if (!formData.email) {
      newErrors.email = isEnglish ? 'Email is required' : '邮箱是必填项';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = isEnglish ? 'Email is invalid' : '邮箱格式无效';
    }

    // 密码验证
    if (!formData.password) {
      newErrors.password = isEnglish ? 'Password is required' : '密码是必填项';
    } else if (formData.password.length < 6) {
      newErrors.password = isEnglish ? 'Password must be at least 6 characters' : '密码至少需要6个字符';
    }

    // 注册时的额外验证
    if (!isLogin) {
      if (!formData.username) {
        newErrors.username = isEnglish ? 'Username is required' : '用户名是必填项';
      } else if (formData.username.length < 3) {
        newErrors.username = isEnglish ? 'Username must be at least 3 characters' : '用户名至少需要3个字符';
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = isEnglish ? 'Please confirm your password' : '请确认密码';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = isEnglish ? 'Passwords do not match' : '密码不匹配';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单输入
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // 检测密码强度
    if (name === 'password' && !isLogin) {
      setPasswordStrength(checkPasswordStrength(value));
    }

    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // 更新表单进度
  React.useEffect(() => {
    setFormProgress(calculateFormProgress());
  }, [formData, isLogin]);

  // 处理表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();

    // 验证表单
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const { authAPI } = await import('../services/api');

      if (isLogin) {
        // 登录
        const response = await authAPI.login({
          email: formData.email,
          password: formData.password
        });

        if (response.success) {
          // 登录成功，跳转到主页面
          navigate('/main');
        }
      } else {
        // 注册
        const response = await authAPI.register({
          email: formData.email,
          password: formData.password,
          username: formData.username,
          preferred_language: language
        });

        if (response.success) {
          // 注册成功，跳转到主页面
          navigate('/main');
        }
      }
    } catch (error) {
      console.error('认证错误:', error);
      setErrors({
        submit: error.message || (isLogin ? '登录失败，请检查邮箱和密码' : '注册失败，请重试')
      });
    } finally {
      setIsLoading(false);
    }
  };

  // 切换登录/注册模式
  const toggleMode = () => {
    setIsLogin(!isLogin);
    setErrors({});
    setPasswordStrength('');
    setFormData({
      email: '',
      password: '',
      confirmPassword: '',
      username: ''
    });
  };

  // 切换语言
  const toggleLanguage = () => {
    const newLang = language === 'en' ? 'zh' : 'en';
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
  };

  return (
    <div className="login-page" data-lang={language}>
      {/* 3D背景 */}
      <div className="login-background">
        <Background3D />
      </div>

      {/* 顶部导航 */}
      <div className="login-header">
        <button
          className="back-button"
          onClick={() => navigate('/')}
        >
          <span className="back-icon">←</span>
          {t.backToHome}
        </button>

        <button
          className="language-toggle"
          onClick={toggleLanguage}
        >
          <span className="language-icon">🌐</span>
          {isEnglish ? '中文' : 'EN'}
        </button>
      </div>

      {/* 主要内容区域 */}
      <div className="login-container">
        <div className="login-content">
          {/* 左侧品牌区域 */}
          <div className="brand-section">
            <div className="brand-content">
              <h1 className="brand-title">
                <span className="welcome-text">{t.welcome}</span>
                <span className="app-name">{t.appName}</span>
              </h1>
              <p className="brand-subtitle">{t.subtitle}</p>

              {/* 装饰性元素 */}
              <div className="brand-decoration">
                <div className="tech-lines">
                  <div className="tech-line"></div>
                  <div className="tech-line"></div>
                  <div className="tech-line"></div>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧表单区域 */}
          <div className="form-section">
            <div className="form-container">
              {/* 表单进度指示器 */}
              <div className="form-progress">
                <div
                  className="form-progress-bar"
                  style={{ width: `${formProgress}%` }}
                ></div>
              </div>

              <div className="form-header">
                <h2 className="form-title">
                  {isLogin ? t.loginTitle : t.registerTitle}
                </h2>
                <p className="form-subtitle">
                  {isLogin
                    ? (isEnglish ? 'Welcome back to the platform' : '欢迎回到平台')
                    : (isEnglish ? 'Join our advanced analytics platform' : '加入我们的高级分析平台')
                  }
                </p>
              </div>

              <form className="login-form" onSubmit={handleSubmit}>
                {!isLogin && (
                  <div className="form-group">
                    <label htmlFor="username">{t.username}</label>
                    <input
                      type="text"
                      id="username"
                      name="username"
                      value={formData.username}
                      onChange={handleInputChange}
                      required={!isLogin}
                      placeholder={t.username}
                      className={errors.username ? 'error' : ''}
                    />
                    {errors.username && <div className="error-message">{errors.username}</div>}
                  </div>
                )}

                <div className="form-group">
                  <label htmlFor="email">{t.email}</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    placeholder={t.email}
                    className={errors.email ? 'error' : ''}
                  />
                  {errors.email && <div className="error-message">{errors.email}</div>}
                </div>

                <div className="form-group">
                  <label htmlFor="password">{t.password}</label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    placeholder={t.password}
                    className={errors.password ? 'error' : ''}
                  />
                  {errors.password && <div className="error-message">{errors.password}</div>}

                  {/* 密码强度指示器 - 仅在注册时显示 */}
                  {!isLogin && formData.password && (
                    <div className={`password-strength ${passwordStrength}`}>
                      <div className="password-strength-bar"></div>
                    </div>
                  )}
                </div>

                {!isLogin && (
                  <div className="form-group">
                    <label htmlFor="confirmPassword">{t.confirmPassword}</label>
                    <input
                      type="password"
                      id="confirmPassword"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      required={!isLogin}
                      placeholder={t.confirmPassword}
                      className={errors.confirmPassword ? 'error' : ''}
                    />
                    {errors.confirmPassword && <div className="error-message">{errors.confirmPassword}</div>}
                  </div>
                )}

                {isLogin && (
                  <div className="form-options">
                    <label className="checkbox-label">
                      <input type="checkbox" />
                      <span className="checkmark"></span>
                      {t.rememberMe}
                    </label>
                    <a href="#" className="forgot-password">{t.forgotPassword}</a>
                  </div>
                )}

                {/* 显示提交错误信息 */}
                {errors.submit && (
                  <div className="error-message submit-error">
                    {errors.submit}
                  </div>
                )}

                <button
                  type="submit"
                  className="submit-button"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="loading-spinner"></div>
                  ) : (
                    isLogin ? t.loginButton : t.registerButton
                  )}
                </button>

                <div className="form-divider">
                  <span>{t.orDivider}</span>
                </div>

                <div className="social-login">
                  <button type="button" className="social-button google">
                    <span className="social-icon">G</span>
                    {t.googleLogin}
                  </button>
                  <button type="button" className="social-button github">
                    <span className="social-icon">⚡</span>
                    {t.githubLogin}
                  </button>
                </div>

                <div className="form-switch">
                  <button
                    type="button"
                    className="switch-button"
                    onClick={toggleMode}
                  >
                    {isLogin ? t.switchToRegister : t.switchToLogin}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
