import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { authAPI, projectAPI } from '../services/api';
import '../styles/AIAnalyzerPage.css';

const AIAnalyzerPage = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [language, setLanguage] = useState(localStorage.getItem('preferredLanguage') || 'en');
  const [activeTab, setActiveTab] = useState('all'); // 'all', 'my', 'favorites'
  const [newProject, setNewProject] = useState({
    name: '',
    description: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);

  // 翻译对象
  const translations = {
    en: {
      title: 'AI Site Analyzer',
      subtitle: 'Intelligent Industrial Site Analysis Platform',
      createProject: 'Create New Project',
      projectName: 'Project Name',
      projectDescription: 'Project Description',
      create: 'Create',
      cancel: 'Cancel',
      noProjects: 'No projects yet',
      noProjectsDesc: 'Create your first project to start analyzing industrial sites',
      createdAt: 'Created',
      openProject: 'Open Project',
      deleteProject: 'Delete',
      backToHome: 'Back to Home',
      selectLanguage: 'Select Language',
      myProjects: 'My Projects',
      allProjects: 'All Projects',
      favorites: 'Favorites',
      starProject: 'Star',
      unstarProject: 'Unstar',
      noFavorites: 'No favorite projects yet',
      noFavoritesDesc: 'Star your important projects to access them quickly'
    },
    zh: {
      title: 'AI选址分析器',
      subtitle: '智能工业选址分析平台',
      createProject: '创建新项目',
      projectName: '项目名称',
      projectDescription: '项目描述',
      create: '创建',
      cancel: '取消',
      noProjects: '暂无项目',
      noProjectsDesc: '创建您的第一个项目开始分析工业选址',
      createdAt: '创建时间',
      openProject: '打开项目',
      deleteProject: '删除',
      backToHome: '返回首页',
      selectLanguage: '选择语言',
      myProjects: '我的项目',
      allProjects: '所有项目',
      favorites: '收藏项目',
      starProject: '收藏',
      unstarProject: '取消收藏',
      noFavorites: '暂无收藏项目',
      noFavoritesDesc: '收藏重要项目以便快速访问'
    }
  };

  const t = translations[language] || translations.en;

  // 检查用户认证状态
  useEffect(() => {
    const checkAuth = async () => {
      if (!authAPI.isAuthenticated()) {
        navigate('/login');
        return;
      }

      const user = authAPI.getUser();
      setCurrentUser(user);
    };

    checkAuth();
  }, [navigate]);

  // 加载项目列表
  useEffect(() => {
    const loadProjects = async () => {
      if (!authAPI.isAuthenticated()) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await projectAPI.getProjects();
        if (response.success) {
          // 转换数据库字段到前端格式
          const formattedProjects = response.data.projects.map(project => ({
            id: project.id.toString(),
            name: project.project_name,
            description: project.description || '',
            createdAt: project.created_at,
            updatedAt: project.updated_at,
            status: project.status,
            starred: false // 暂时设为false，后续可以添加收藏功能
          }));
          setProjects(formattedProjects);
        }
      } catch (error) {
        console.error('加载项目失败:', error);
        setError('加载项目失败，请重试');
        // 如果是认证错误，跳转到登录页
        if (error.message.includes('401') || error.message.includes('403')) {
          navigate('/login');
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadProjects();
  }, [navigate]);

  // 创建新项目
  const handleCreateProject = useCallback(async () => {
    if (!newProject.name.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await projectAPI.createProject({
        project_name: newProject.name.trim(),
        description: newProject.description.trim()
      });

      if (response.success) {
        // 转换数据库字段到前端格式
        const formattedProject = {
          id: response.data.project.id.toString(),
          name: response.data.project.project_name,
          description: response.data.project.description || '',
          createdAt: response.data.project.created_at,
          updatedAt: response.data.project.updated_at,
          status: response.data.project.status,
          starred: false
        };

        setProjects(prev => [formattedProject, ...prev]);
        setNewProject({ name: '', description: '' });
        setShowCreateModal(false);
      }
    } catch (error) {
      console.error('创建项目失败:', error);
      setError('创建项目失败，请重试');
    } finally {
      setIsLoading(false);
    }
  }, [newProject]);

  // 删除项目
  const handleDeleteProject = useCallback(async (projectId) => {
    if (!confirm(language === 'zh' ? '确定要删除这个项目吗？' : 'Are you sure you want to delete this project?')) {
      return;
    }

    try {
      const response = await projectAPI.deleteProject(projectId);
      if (response.success) {
        setProjects(prev => prev.filter(p => p.id !== projectId));
      }
    } catch (error) {
      console.error('删除项目失败:', error);
      setError('删除项目失败，请重试');
    }
  }, [language]);

  // 打开项目
  const handleOpenProject = useCallback((projectId) => {
    navigate(`/ai-project/${projectId}`);
  }, [navigate]);

  // 切换项目收藏状态 (本地状态，暂不同步到数据库)
  const toggleProjectStar = useCallback((projectId) => {
    setProjects(prev => prev.map(project =>
      project.id === projectId
        ? { ...project, starred: !project.starred }
        : project
    ));
  }, []);

  // 语言切换
  const handleLanguageChange = useCallback((newLang) => {
    setLanguage(newLang);
    localStorage.setItem('preferredLanguage', newLang);
  }, []);

  // 根据当前选项卡筛选项目
  const filteredProjects = useCallback(() => {
    switch(activeTab) {
      case 'favorites':
        return projects.filter(p => p.starred);
      case 'my':
        // 这里可以根据用户ID等筛选"我的项目"
        // 目前只是演示，所以返回所有项目
        return projects;
      case 'all':
      default:
        return projects;
    }
  }, [projects, activeTab]);

  const displayedProjects = filteredProjects();

  return (
    <div className="ai-analyzer-page">
      {/* 背景效果 - 增强版 */}
      <div className="ai-background">
        <div className="grid-pattern"></div>
        <div className="floating-particles"></div>
        <div className="circuit-pattern"></div>
        <div className="glow-effect"></div>
      </div>

      {/* 顶部导航 */}
      <header className="ai-header">
        <div className="header-content">
          <button
            className="back-button"
            onClick={() => navigate('/')}
          >
            ← {t.backToHome}
          </button>

          <div className="header-center">
            <h1 className="page-title">{t.title}</h1>
            <p className="page-subtitle">{t.subtitle}</p>
          </div>

          {/* 语言切换按钮 */}
          <div className="language-switcher">
            <button
              onClick={() => {
                const newLang = language === 'en' ? 'zh' : 'en';
                handleLanguageChange(newLang);
              }}
              className="language-button"
            >
              {language === 'en' ? '🇺🇸 English' : '🇨🇳 中文'}
            </button>
          </div>
        </div>
      </header>

      {/* 主内容 - 重新设计 */}
      <main className="ai-main">
        <div className="ai-container">
          {/* 项目控制区 - 简化版 */}
          <div className="projects-control">
            <button
              className="create-project-btn pulse-effect"
              onClick={() => setShowCreateModal(true)}
            >
              <span className="btn-icon">+</span>
              {t.createProject}
            </button>
            
            <div className="view-filters">
              <button 
                className={`filter-btn ${activeTab === 'all' ? 'active' : ''}`}
                onClick={() => setActiveTab('all')}
              >
                <span className="filter-icon">🔍</span>
                {t.allProjects}
              </button>
              <button 
                className={`filter-btn ${activeTab === 'favorites' ? 'active' : ''}`}
                onClick={() => setActiveTab('favorites')}
              >
                <span className="filter-icon">⭐</span>
                {t.favorites}
              </button>
            </div>
          </div>

          {/* 错误信息显示 */}
          {error && (
            <div className="error-banner">
              <span className="error-icon">⚠️</span>
              <span className="error-text">{error}</span>
              <button
                className="error-close"
                onClick={() => setError(null)}
              >
                ×
              </button>
            </div>
          )}

          {/* 项目列表 - 增强版 */}
          <div className="projects-gallery">
            {isLoading ? (
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p className="loading-text">
                  {language === 'zh' ? '正在加载项目...' : 'Loading projects...'}
                </p>
              </div>
            ) : displayedProjects.length === 0 ? (
              <div className="empty-projects">
                <div className="empty-animation">
                  <div className="pulse-circle"></div>
                  <div className="empty-icon">
                    {activeTab === 'favorites' ? '⭐' : '📊'}
                  </div>
                </div>
                <h3 className="empty-title">
                  {activeTab === 'favorites' ? t.noFavorites : t.noProjects}
                </h3>
                <p className="empty-description">
                  {activeTab === 'favorites' ? t.noFavoritesDesc : t.noProjectsDesc}
                </p>
                {activeTab !== 'favorites' && (
                  <button
                    className="empty-create-btn glow-on-hover"
                    onClick={() => setShowCreateModal(true)}
                  >
                    {t.createProject}
                  </button>
                )}
              </div>
            ) : (
              <div className="projects-grid">
                {displayedProjects.map(project => (
                  <div key={project.id} className="project-card">
                    <div className="card-glow"></div>
                    <div className="project-content" onClick={() => handleOpenProject(project.id)}>
                      <div className="project-header">
                        <h3 className="project-name">{project.name}</h3>
                        <button 
                          className={`star-btn ${project.starred ? 'starred' : ''}`}
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleProjectStar(project.id);
                          }}
                          title={project.starred ? t.unstarProject : t.starProject}
                        >
                          {project.starred ? '★' : '☆'}
                        </button>
                      </div>
                      {project.description && (
                        <p className="project-description">{project.description}</p>
                      )}
                      <div className="project-meta">
                        <span className="project-date">
                          {t.createdAt}: {new Date(project.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                    <div className="project-actions">
                      <button
                        className="action-btn open-btn"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleOpenProject(project.id);
                        }}
                        title={t.openProject}
                      >
                        <span className="action-icon">📂</span>
                        <span className="action-text">{t.openProject}</span>
                      </button>
                      <button
                        className="action-btn delete-btn"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteProject(project.id);
                        }}
                        title={t.deleteProject}
                      >
                        <span className="action-icon">🗑️</span>
                        <span className="action-text">{t.deleteProject}</span>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>

      {/* 创建项目模态框 */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2>{t.createProject}</h2>
            <div className="form-group">
              <label>{t.projectName}</label>
              <input
                type="text"
                value={newProject.name}
                onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
                placeholder={t.projectName}
                maxLength={100}
              />
            </div>
            <div className="form-group">
              <label>{t.projectDescription}</label>
              <textarea
                value={newProject.description}
                onChange={(e) => setNewProject(prev => ({ ...prev, description: e.target.value }))}
                placeholder={t.projectDescription}
                rows={3}
                maxLength={500}
              />
            </div>
            <div className="modal-actions">
              <button
                className="btn-secondary"
                onClick={() => {
                  setShowCreateModal(false);
                  setNewProject({ name: '', description: '' });
                }}
              >
                {t.cancel}
              </button>
              <button
                className="btn-primary"
                onClick={handleCreateProject}
                disabled={!newProject.name.trim()}
              >
                {t.create}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIAnalyzerPage;
