/* AI Analyzer Page Styles - 优化版 */
.ai-analyzer-page {
  min-height: 100vh;
  position: relative;
  background-color: #0c1221;
  color: #e5e7eb;
  font-family: 'Inter', system-ui, sans-serif;
  overflow-x: hidden;
  z-index: 0; /* 确保正确的层叠顺序 */
}

/* 确保语言下拉菜单显示 */
.ai-analyzer-page .language-dropdown,
.ai-analyzer-page .language-dropdown-trigger {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 背景效果 - 增强版 */
.ai-background {
  position: fixed; /* 改为fixed以确保全屏覆盖 */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1; /* 确保在内容后面 */
  overflow: hidden;
  pointer-events: none; /* 防止背景干扰交互 */
}

.grid-pattern {
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background-image: 
    linear-gradient(rgba(29, 78, 216, 0.07) 1px, transparent 1px),
    linear-gradient(90deg, rgba(29, 78, 216, 0.07) 1px, transparent 1px);
  background-size: 30px 30px;
  transform: perspective(500px) rotateX(60deg);
  animation: grid-movement 60s linear infinite;
  opacity: 0.5; /* 增加不透明度 */
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(rgba(59, 130, 246, 0.2) 2px, transparent 2px),
    radial-gradient(rgba(16, 185, 129, 0.2) 2px, transparent 2px);
  background-size: 60px 60px;
  background-position: 0 0, 30px 30px;
  animation: particle-movement 120s linear infinite;
  opacity: 0.4; /* 增加不透明度 */
}

/* 电路图背景 - 增强版 */
.circuit-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.3; /* 增加不透明度 */
  overflow: hidden;
  pointer-events: none;
}

.circuit-pattern::before,
.circuit-pattern::after {
  content: '';
  position: absolute;
  background-repeat: no-repeat;
  background-size: contain;
  animation: circuit-pulse 4s ease-in-out infinite alternate;
}

.circuit-pattern::before {
  top: 5%;
  right: 5%;
  width: 600px;
  height: 600px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 800' width='800' height='800'%3E%3Cpath fill='none' stroke='%233b82f6' stroke-width='3' d='M200,100 L400,100 L400,200 M400,100 L600,100 L600,300 M600,350 L600,500 L450,500 M400,500 L250,500 L250,650 M250,700 L250,750 L650,750 M400,750 L400,500 M450,100 L450,300 L600,300 M50,300 L200,300 L200,100 M50,300 L50,700 L250,700 M650,750 L650,250 L450,250 L450,300 M50,500 L200,500 L200,600 M400,300 C250,300 250,450 400,450 C550,450 550,300 400,300 M400,250 L400,300 M400,450 L400,500'/%3E%3C/svg%3E");
  transform: rotate(5deg);
  filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6)); /* 增强阴影 */
}

.circuit-pattern::after {
  bottom: 10%;
  left: 5%;
  width: 500px;
  height: 500px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 800 800' width='800' height='800'%3E%3Cpath fill='none' stroke='%2334d399' stroke-width='3' d='M400,50 L400,150 M400,200 L400,300 M400,200 C250,200 250,500 400,500 M400,500 C550,500 550,200 400,200 M400,500 L400,600 M400,650 L400,750 M300,150 L500,150 M300,750 L500,750 M100,300 L300,300 M100,600 L300,600 M500,300 L700,300 M500,600 L700,600 M100,300 C50,300 50,600 100,600 M700,300 C750,300 750,600 700,600 M250,400 L350,400 M450,400 L550,400 M400,350 L400,450'/%3E%3C/svg%3E");
  transform: rotate(-10deg);
  filter: drop-shadow(0 0 20px rgba(52, 211, 153, 0.6)); /* 增强阴影 */
  animation-delay: 2s;
}

/* 添加全屏电路背景 */
.ai-analyzer-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25'%3E%3Cdefs%3E%3Cpattern id='circuit' patternUnits='userSpaceOnUse' width='500' height='500' patternTransform='scale(0.75) rotate(0)'%3E%3Cpath fill='none' stroke='%233b82f680' stroke-width='1' d='M100,100 L100,400 L400,400 M250,100 L250,250 L400,250 M400,100 L400,400 M100,250 L175,250 M325,250 L400,250 M250,250 C250,200 300,200 300,250 C300,300 250,300 250,250 M0,0 L0,500 M500,0 L500,500 M0,0 L500,0 M0,500 L500,500'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100%25' height='100%25' fill='url(%23circuit)' opacity='0.12'/%3E%3C/svg%3E"); /* 增加不透明度 */
  z-index: -2; /* 确保在背景层后面 */
  opacity: 1;
  pointer-events: none;
}

/* 新增发光效果 */
.glow-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.15), transparent 70%);
  pointer-events: none;
  opacity: 0.8;
  animation: glow-pulse 8s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes circuit-pulse {
  0% {
    opacity: 0.2;
    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.3));
  }
  100% {
    opacity: 0.5; /* 增加最大不透明度 */
    filter: drop-shadow(0 0 25px rgba(59, 130, 246, 0.7)); /* 增强阴影 */
  }
}

@keyframes grid-movement {
  0% {
    transform: perspective(500px) rotateX(60deg) translateY(0);
  }
  100% {
    transform: perspective(500px) rotateX(60deg) translateY(30px);
  }
}

@keyframes particle-movement {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100px);
  }
}

/* 顶部导航 */
.ai-header {
  position: relative;
  z-index: 1;
  padding: 1.5rem 2rem;
  background: rgba(13, 18, 33, 0.85);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(55, 65, 81, 0.5);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(55, 65, 81, 0.3);
  color: #e5e7eb;
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-button:hover {
  background: rgba(55, 65, 81, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-center {
  text-align: center;
}

.page-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(90deg, #60a5fa, #34d399);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  text-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.page-subtitle {
  margin: 0.5rem 0 0;
  font-size: 0.875rem;
  color: #9ca3af;
  font-weight: 400;
}

/* 语言下拉菜单 */
.language-switcher {
  position: relative;
}

.language-button {
  background: rgba(55, 65, 81, 0.3);
  border: 1px solid rgba(75, 85, 99, 0.5);
  color: #e5e7eb;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.language-button:hover {
  background: rgba(55, 65, 81, 0.5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 主内容 */
.ai-main {
  position: relative;
  z-index: 1;
  padding: 2rem;
}

.ai-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 项目控制区 - 重新设计 */
.projects-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: rgba(22, 31, 49, 0.75);
  border-radius: 16px;
  border: 1px solid rgba(75, 85, 99, 0.5);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.projects-control::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.8), rgba(52, 211, 153, 0.8), transparent);
  animation: control-glow 4s ease-in-out infinite;
}

.projects-control::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), rgba(52, 211, 153, 0.3), transparent);
}

@keyframes control-glow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

/* 创建项目按钮 - 增强版 */
.create-project-btn {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(79, 70, 229, 0.9));
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3), 0 2px 5px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.create-project-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.5s ease;
  z-index: -1;
}

.create-project-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4), 0 4px 8px rgba(0, 0, 0, 0.2);
}

.create-project-btn:hover::before {
  left: 100%;
}

.pulse-effect {
  animation: button-pulse 2s infinite;
}

@keyframes button-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(37, 99, 235, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(37, 99, 235, 0);
  }
}

.btn-icon {
  font-size: 1.25rem;
  font-weight: 700;
}

/* 视图过滤器 - 新设计 */
.view-filters {
  display: flex;
  gap: 1rem;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(31, 41, 55, 0.5);
  color: #9ca3af;
  border: 1px solid rgba(75, 85, 99, 0.3);
  border-radius: 10px;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: rgba(31, 41, 55, 0.7);
  color: #e5e7eb;
  border-color: rgba(75, 85, 99, 0.5);
  transform: translateY(-2px);
}

.filter-btn.active {
  background: rgba(37, 99, 235, 0.2);
  color: #60a5fa;
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.3);
}

.filter-icon {
  font-size: 1rem;
}

/* 项目列表 - 重新设计 */
.projects-gallery {
  padding: 1rem 0;
}

/* 空项目状态 - 增强版 */
.empty-projects {
  background: rgba(22, 31, 49, 0.75);
  border-radius: 16px;
  border: 1px solid rgba(75, 85, 99, 0.5);
  padding: 4rem 2rem;
  text-align: center;
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.empty-projects::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.7), rgba(52, 211, 153, 0.7), transparent);
}

.empty-animation {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
}

.pulse-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, transparent 70%);
  animation: circle-pulse 3s ease-in-out infinite;
}

@keyframes circle-pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
}

.empty-icon {
  position: relative;
  z-index: 1;
  font-size: 3.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1rem;
  color: #e5e7eb;
  background: linear-gradient(90deg, #60a5fa, #34d399);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.empty-description {
  color: #9ca3af;
  margin: 0 0 2.5rem;
  max-width: 450px;
  margin-left: auto;
  margin-right: auto;
  font-size: 1rem;
  line-height: 1.6;
}

.empty-create-btn {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(79, 70, 229, 0.9));
  color: white;
  border: none;
  border-radius: 12px;
  padding: 0.75rem 1.75rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3), 0 2px 5px rgba(0, 0, 0, 0.2);
}

.glow-on-hover {
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.glow-on-hover::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.4) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

.glow-on-hover:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(37, 99, 235, 0.4), 0 4px 8px rgba(0, 0, 0, 0.2);
}

.glow-on-hover:hover::after {
  opacity: 1;
}

/* 项目网格 - 增强版 */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
  gap: 2rem;
}

/* 项目卡片 - 增强版 */
.project-card {
  background: rgba(22, 31, 49, 0.75);
  border-radius: 16px;
  border: 1px solid rgba(75, 85, 99, 0.5);
  overflow: hidden;
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.15), transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease;
}

.project-card:hover {
  transform: translateY(-6px) scale(1.02);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.25), 0 10px 15px rgba(0, 0, 0, 0.15);
  border-color: rgba(59, 130, 246, 0.6);
}

.project-card:hover .card-glow {
  opacity: 1;
}

.project-content {
  padding: 1.5rem;
  flex: 1;
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

.project-header {
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.project-name {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
  color: #e5e7eb;
  flex: 1;
}

/* 收藏按钮 */
.star-btn {
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 1.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 0.25rem;
  line-height: 1;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.star-btn:hover {
  color: #f59e0b;
  transform: scale(1.1);
}

.star-btn.starred {
  color: #f59e0b;
  text-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.project-description {
  color: #9ca3af;
  font-size: 0.875rem;
  margin: 0 0 1rem;
  line-height: 1.5;
  flex: 1;
}

.project-meta {
  margin-top: auto;
}

.project-date {
  font-size: 0.75rem;
  color: #6b7280;
}

.project-actions {
  display: flex;
  border-top: 1px solid rgba(75, 85, 99, 0.5);
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: none;
  border: none;
  color: #9ca3af;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:first-child {
  border-right: 1px solid rgba(75, 85, 99, 0.5);
}

.action-btn:hover {
  background: rgba(55, 65, 81, 0.5);
  color: #e5e7eb;
}

.open-btn:hover {
  color: #3b82f6;
}

.delete-btn:hover {
  color: #ef4444;
}

.action-icon {
  font-size: 0.875rem;
}

.action-text {
  font-size: 0.75rem;
  font-weight: 500;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 1rem;
}

.modal-content {
  background: #1f2937;
  border-radius: 12px;
  padding: 2rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(75, 85, 99, 0.5);
  position: relative;
  overflow: hidden;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3b82f6, #34d399, transparent);
}

.modal-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 1.5rem;
  color: #e5e7eb;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #e5e7eb;
}

input[type="text"],
textarea {
  width: 100%;
  padding: 0.75rem;
  background: #374151;
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 8px;
  color: #e5e7eb;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

input[type="text"]:focus,
textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.btn-secondary {
  background: rgba(55, 65, 81, 0.5);
  color: #e5e7eb;
  border: 1px solid rgba(75, 85, 99, 0.5);
  border-radius: 8px;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  background: rgba(55, 65, 81, 0.7);
}

.btn-primary {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.8), rgba(79, 70, 229, 0.8));
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(79, 70, 229, 0.9));
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15), 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ai-header {
    padding: 1rem;
  }
  
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }
  
  .back-button {
    align-self: flex-start;
  }
  
  .language-switcher {
    align-self: flex-end;
  }
  
  .ai-main {
    padding: 1rem;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .create-project-btn {
    width: 100%;
    justify-content: center;
  }
  
  .dashboard-tabs {
    width: 100%;
    justify-content: space-between;
    gap: 0.5rem;
  }
  
  .tab-btn {
    min-width: auto;
    flex: 1;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .modal-content {
    padding: 1.5rem;
  }
  
  .project-actions {
    flex-direction: column;
  }
  
  .action-btn:first-child {
    border-right: none;
    border-bottom: 1px solid rgba(75, 85, 99, 0.5);
  }
}
