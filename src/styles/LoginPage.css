/* LoginPage.css - 登录注册页面样式 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* 页面容器 */
.login-page {
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  flex-direction: column;
}

/* 3D背景 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.8;
}

/* 顶部导航 */
.login-header {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: rgba(15, 25, 40, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(77, 200, 255, 0.1);
}

.back-button,
.language-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(77, 200, 255, 0.1);
  border: 1px solid rgba(77, 200, 255, 0.2);
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 16px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.back-button:hover,
.language-toggle:hover {
  background: rgba(77, 200, 255, 0.2);
  border-color: rgba(77, 200, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.2);
}

.back-icon,
.language-icon {
  font-size: 16px;
}

/* 主要内容容器 */
.login-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  position: relative;
  z-index: 5;
}

.login-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  max-width: 1200px;
  width: 100%;
  align-items: center;
}

/* 左侧品牌区域 */
.brand-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 40px;
}

.brand-content {
  max-width: 500px;
}

.brand-title {
  margin: 0 0 20px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.welcome-text {
  font-family: 'Rajdhani', sans-serif;
  font-size: 24px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
}

.app-name {
  font-family: 'Orbitron', monospace;
  font-size: 48px;
  font-weight: 700;
  background: linear-gradient(135deg, #4dc8ff, #00e5ff, #80d0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 2px;
  text-shadow: 0 0 30px rgba(77, 200, 255, 0.3);
}

.brand-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 40px 0;
  letter-spacing: 0.5px;
  line-height: 1.6;
}

/* 装饰性元素 */
.brand-decoration {
  margin-top: 40px;
}

.tech-lines {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tech-line {
  height: 2px;
  background: linear-gradient(90deg, #4dc8ff, transparent);
  border-radius: 1px;
  animation: techPulse 3s ease-in-out infinite;
}

.tech-line:nth-child(1) {
  width: 120px;
  animation-delay: 0s;
}

.tech-line:nth-child(2) {
  width: 80px;
  animation-delay: 0.5s;
}

.tech-line:nth-child(3) {
  width: 100px;
  animation-delay: 1s;
}

@keyframes techPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.1);
  }
}

/* 右侧表单区域 */
.form-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.form-container {
  background: rgba(15, 25, 40, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 450px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.05), transparent);
  animation: formShimmer 8s ease-in-out infinite;
  z-index: 0;
}

@keyframes formShimmer {
  0%, 90% {
    left: -100%;
  }
  10%, 80% {
    left: 100%;
  }
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
  z-index: 1;
}

.form-title {
  font-family: 'Orbitron', monospace;
  font-size: 28px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  letter-spacing: 1px;
}

/* 表单样式 */
.login-form {
  position: relative;
  z-index: 1;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.form-group input {
  width: 100%;
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(77, 200, 255, 0.2);
  border-radius: 10px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 16px;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #4dc8ff;
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 3px rgba(77, 200, 255, 0.1);
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.forgot-password {
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: #4dc8ff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #80d0ff;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #4dc8ff, #00a5ff);
  border: none;
  border-radius: 10px;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(77, 200, 255, 0.4);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 分割线 */
.form-divider {
  text-align: center;
  margin: 25px 0;
  position: relative;
}

.form-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(77, 200, 255, 0.2);
}

.form-divider span {
  background: rgba(15, 25, 40, 0.9);
  padding: 0 15px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  position: relative;
  z-index: 1;
}

/* 社交登录按钮 */
.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 25px;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.social-icon {
  font-weight: bold;
  font-size: 16px;
}

/* 表单切换 */
.form-switch {
  text-align: center;
}

.switch-button {
  background: none;
  border: none;
  color: #4dc8ff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  text-decoration: underline;
}

.switch-button:hover {
  color: #80d0ff;
}

/* 中文字体优化 */
.login-page[data-lang="zh"] .form-group label,
.login-page[data-lang="zh"] .form-group input,
.login-page[data-lang="zh"] .social-button,
.login-page[data-lang="zh"] .switch-button,
.login-page[data-lang="zh"] .back-button,
.login-page[data-lang="zh"] .language-toggle {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.login-page[data-lang="zh"] .welcome-text,
.login-page[data-lang="zh"] .brand-subtitle {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.5px;
}

/* 表单验证状态 */
.form-group input.error {
  border-color: #ff4757;
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.form-group input.success {
  border-color: #2ed573;
  box-shadow: 0 0 0 3px rgba(46, 213, 115, 0.1);
}

.error-message {
  color: #ff4757;
  font-size: 12px;
  margin-top: 5px;
  font-family: 'Rajdhani', sans-serif;
}

/* 增强的动画效果 */
.login-container {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-container {
  animation: slideInRight 0.6s ease-out 0.2s both;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.brand-section {
  animation: slideInLeft 0.6s ease-out 0.1s both;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .brand-section {
    align-items: center;
    padding: 20px;
  }

  .app-name {
    font-size: 36px;
  }
}

@media (max-width: 768px) {
  .login-header {
    padding: 15px 20px;
  }

  .login-container {
    padding: 20px;
  }

  .form-container {
    padding: 30px 25px;
  }

  .app-name {
    font-size: 28px;
  }

  .welcome-text {
    font-size: 18px;
  }

  .brand-subtitle {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .login-header {
    padding: 10px 15px;
  }

  .back-button,
  .language-toggle {
    padding: 8px 12px;
    font-size: 12px;
  }

  .login-container {
    padding: 15px;
  }

  .form-container {
    padding: 25px 20px;
  }

  .app-name {
    font-size: 24px;
  }

  .form-title {
    font-size: 24px;
  }
}
