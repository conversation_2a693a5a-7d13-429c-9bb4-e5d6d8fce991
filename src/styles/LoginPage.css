/* LoginPage.css - 登录注册页面样式 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* 页面容器 */
.login-page {
  min-height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  flex-direction: column;
}

/* 3D背景 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  opacity: 0.8;
}

/* 顶部导航 */
.login-header {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 40px;
  background: rgba(15, 25, 40, 0.3);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(77, 200, 255, 0.1);
}

.back-button,
.language-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(77, 200, 255, 0.1);
  border: 1px solid rgba(77, 200, 255, 0.2);
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  font-weight: 500;
  padding: 10px 16px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.back-button:hover,
.language-toggle:hover {
  background: rgba(77, 200, 255, 0.2);
  border-color: rgba(77, 200, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(77, 200, 255, 0.2);
}

.back-icon,
.language-icon {
  font-size: 16px;
}

/* 主要内容容器 */
.login-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  z-index: 5;
  min-height: calc(100vh - 80px);
}

.login-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  max-width: 1400px;
  width: 100%;
  align-items: stretch;
  min-height: 600px;
}

/* 左侧品牌区域 */
.brand-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 40px;
}

.brand-content {
  max-width: 500px;
}

.brand-title {
  margin: 0 0 20px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.welcome-text {
  font-family: 'Rajdhani', sans-serif;
  font-size: 24px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 1px;
}

.app-name {
  font-family: 'Orbitron', monospace;
  font-size: 48px;
  font-weight: 700;
  background: linear-gradient(135deg, #4dc8ff, #00e5ff, #80d0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 2px;
  text-shadow: 0 0 30px rgba(77, 200, 255, 0.3);
}

.brand-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 40px 0;
  letter-spacing: 0.5px;
  line-height: 1.6;
}

/* 装饰性元素 */
.brand-decoration {
  margin-top: 40px;
}

.tech-lines {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tech-line {
  height: 2px;
  background: linear-gradient(90deg, #4dc8ff, transparent);
  border-radius: 1px;
  animation: techPulse 3s ease-in-out infinite;
}

.tech-line:nth-child(1) {
  width: 120px;
  animation-delay: 0s;
}

.tech-line:nth-child(2) {
  width: 80px;
  animation-delay: 0.5s;
}

.tech-line:nth-child(3) {
  width: 100px;
  animation-delay: 1s;
}

@keyframes techPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scaleX(1);
  }
  50% {
    opacity: 1;
    transform: scaleX(1.1);
  }
}

/* 右侧表单区域 */
.form-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.form-container {
  background: rgba(15, 25, 40, 0.85);
  backdrop-filter: blur(25px);
  border: 1px solid rgba(77, 200, 255, 0.25);
  border-radius: 24px;
  padding: 32px;
  width: 100%;
  max-width: 520px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow:
    0 25px 80px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.12);
  position: relative;
  transition: all 0.3s ease;
}

.form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.05), transparent);
  animation: formShimmer 8s ease-in-out infinite;
  z-index: 0;
}

@keyframes formShimmer {
  0%, 90% {
    left: -100%;
  }
  10%, 80% {
    left: 100%;
  }
}

.form-header {
  text-align: center;
  margin-bottom: 24px;
  position: relative;
  z-index: 1;
}

.form-title {
  font-family: 'Orbitron', monospace;
  font-size: 26px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 8px 0;
  letter-spacing: 1px;
  background: linear-gradient(135deg, #ffffff, #4dc8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  letter-spacing: 0.5px;
}

/* 表单样式 */
.login-form {
  position: relative;
  z-index: 1;
}

.form-group {
  margin-bottom: 18px;
  position: relative;
}

.form-group label {
  display: block;
  font-family: 'Rajdhani', sans-serif;
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 6px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(77, 200, 255, 0.25);
  border-radius: 12px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  position: relative;
}

.form-group input:focus {
  outline: none;
  border-color: #4dc8ff;
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 3px rgba(77, 200, 255, 0.15);
  transform: translateY(-1px);
}

.form-group input:hover:not(:focus) {
  border-color: rgba(77, 200, 255, 0.4);
  background: rgba(255, 255, 255, 0.08);
}

.form-group input::placeholder {
  color: rgba(255, 255, 255, 0.4);
  transition: color 0.3s ease;
}

.form-group input:focus::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.forgot-password {
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: #4dc8ff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: #80d0ff;
}

/* 提交按钮 */
.submit-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #4dc8ff, #00a5ff);
  border: none;
  border-radius: 10px;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(77, 200, 255, 0.4);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 分割线 */
.form-divider {
  text-align: center;
  margin: 25px 0;
  position: relative;
}

.form-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: rgba(77, 200, 255, 0.2);
}

.form-divider span {
  background: rgba(15, 25, 40, 0.9);
  padding: 0 15px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  position: relative;
  z-index: 1;
}

/* 社交登录按钮 */
.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 25px;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: #ffffff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.social-icon {
  font-weight: bold;
  font-size: 16px;
}

/* 表单切换 */
.form-switch {
  text-align: center;
}

.switch-button {
  background: none;
  border: none;
  color: #4dc8ff;
  font-family: 'Rajdhani', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
  text-decoration: underline;
}

.switch-button:hover {
  color: #80d0ff;
}

/* 中文字体优化 */
.login-page[data-lang="zh"] .form-group label,
.login-page[data-lang="zh"] .form-group input,
.login-page[data-lang="zh"] .social-button,
.login-page[data-lang="zh"] .switch-button,
.login-page[data-lang="zh"] .back-button,
.login-page[data-lang="zh"] .language-toggle {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
}

.login-page[data-lang="zh"] .welcome-text,
.login-page[data-lang="zh"] .brand-subtitle {
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  letter-spacing: 0.5px;
}

/* 表单验证状态 */
.form-group input.error {
  border-color: #ff6b7a;
  background: rgba(255, 107, 122, 0.08);
  box-shadow: 0 0 0 3px rgba(255, 107, 122, 0.15);
  animation: shakeError 0.5s ease-in-out;
}

.form-group input.success {
  border-color: #4ecdc4;
  background: rgba(78, 205, 196, 0.08);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.15);
}

.error-message {
  color: #ff6b7a;
  font-size: 11px;
  margin-top: 6px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  animation: slideInError 0.3s ease-out;
}

.error-message::before {
  content: '⚠';
  font-size: 12px;
}

.success-message {
  color: #4ecdc4;
  font-size: 11px;
  margin-top: 6px;
  font-family: 'Rajdhani', sans-serif;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  animation: slideInSuccess 0.3s ease-out;
}

.success-message::before {
  content: '✓';
  font-size: 12px;
}

@keyframes shakeError {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-4px); }
  75% { transform: translateX(4px); }
}

@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInSuccess {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 增强的动画效果 */
.login-container {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义滚动条 */
.form-container::-webkit-scrollbar {
  width: 6px;
}

.form-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb {
  background: rgba(77, 200, 255, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background: rgba(77, 200, 255, 0.5);
}

/* 表单分组样式 */
.form-section-divider {
  margin: 24px 0;
  text-align: center;
  position: relative;
}

.form-section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(77, 200, 255, 0.3), transparent);
}

.form-section-title {
  background: rgba(15, 25, 40, 0.9);
  padding: 0 12px;
  font-family: 'Rajdhani', sans-serif;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  z-index: 1;
}

/* 增强的提交按钮 */
.submit-button {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #4dc8ff, #00a5ff, #0088cc);
  border: none;
  border-radius: 12px;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  font-size: 15px;
  font-weight: 600;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  margin-bottom: 20px;
  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.3);
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(77, 200, 255, 0.4);
  background: linear-gradient(135deg, #5dd3ff, #10b5ff, #1199dd);
}

.submit-button:hover:not(:disabled)::before {
  left: 100%;
}

.submit-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(77, 200, 255, 0.3);
}

.submit-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 响应式设计 */
@media screen and (max-width: 1024px) {
  .login-content {
    grid-template-columns: 1fr;
    gap: 40px;
    max-width: 600px;
    min-height: auto;
  }

  .brand-section {
    order: 2;
    padding: 20px;
    text-align: center;
  }

  .form-section {
    order: 1;
  }

  .form-container {
    max-width: 100%;
    margin: 0 auto;
  }

  .app-name {
    font-size: 36px;
  }

  .welcome-text {
    font-size: 20px;
  }
}

@media screen and (max-width: 768px) {
  .login-container {
    padding: 15px;
    min-height: calc(100vh - 70px);
  }

  .login-header {
    padding: 15px 20px;
  }

  .form-container {
    padding: 24px;
    border-radius: 20px;
    max-height: 90vh;
  }

  .form-title {
    font-size: 22px;
  }

  .app-name {
    font-size: 28px;
  }

  .welcome-text {
    font-size: 18px;
  }

  .brand-subtitle {
    font-size: 16px;
  }

  .form-group {
    margin-bottom: 16px;
  }

  .form-group input {
    padding: 11px 14px;
    font-size: 14px;
  }

  .submit-button {
    padding: 12px;
    font-size: 14px;
  }
}

@media screen and (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .form-container {
    padding: 20px;
    border-radius: 16px;
  }

  .form-title {
    font-size: 20px;
  }

  .app-name {
    font-size: 24px;
  }

  .welcome-text {
    font-size: 16px;
  }

  .brand-subtitle {
    font-size: 14px;
  }

  .form-group input {
    padding: 10px 12px;
  }

  .social-login {
    flex-direction: column;
    gap: 10px;
  }

  .social-button {
    padding: 10px;
    font-size: 13px;
  }
}

/* 动画增强 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.form-container {
  animation: slideInRight 0.6s ease-out 0.2s both;
}

.brand-section {
  animation: slideInLeft 0.6s ease-out both;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 6px;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.password-strength-bar {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.password-strength.weak .password-strength-bar {
  width: 33%;
  background: linear-gradient(90deg, #ff6b7a, #ff8a95);
}

.password-strength.medium .password-strength-bar {
  width: 66%;
  background: linear-gradient(90deg, #ffa726, #ffcc02);
}

.password-strength.strong .password-strength-bar {
  width: 100%;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

/* 表单进度指示器 */
.form-progress {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 24px 24px 0 0;
  overflow: hidden;
}

.form-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4dc8ff, #00a5ff);
  transition: width 0.3s ease;
  border-radius: 3px;
}

/* 输入框图标 */
.form-group.with-icon {
  position: relative;
}

.form-group.with-icon input {
  padding-left: 40px;
}

.form-group.with-icon .input-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.4);
  font-size: 16px;
  transition: color 0.3s ease;
  z-index: 1;
}

.form-group.with-icon input:focus + .input-icon {
  color: #4dc8ff;
}
